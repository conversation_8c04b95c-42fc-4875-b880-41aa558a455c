import {
  Box,
  Chip,
  TextField,
  Autocomplete,
  Typography,
  styled,
} from "@mui/material";
import Tooltip, { TooltipProps, tooltipClasses } from "@mui/material/Tooltip";
import { RenderFormFields } from "@/presentation/components/features/professional/carnetSante/RenderFormFields";
import { useCarnetDeSante } from "@/presentation/hooks/carnetDeSante";
import { TITRES_CARNET_DE_SANTE } from "@/shared/constants/TitreCarnetDeSante";
import { ChevronRight, Info } from "lucide-react";
import { useState, useCallback, useMemo, useEffect } from "react";
import {
  AutocompleteChangeReason,
  AutocompleteInputChangeReason,
} from "@mui/material/Autocomplete";
import { useOptionsCarnetDeSante } from "@/presentation/hooks/carnetDeSante/sousCarnet/useOptionsCarnetDeSante";

interface CarnetDeSanteFormProps {
  type: string;
}

// Type pour les options
interface Option {
  nom: string;
  dosage?: string;
  forme?: string;
}

const LightTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: theme.palette.common.white,
    color: "rgba(0, 0, 0, 0.87)",
    boxShadow: theme.shadows[3],
  },
}));

export const CarnetDeSanteForm = ({ type }: CarnetDeSanteFormProps) => {
  const { getOption } = useOptionsCarnetDeSante(type);
  const { selectedSearch, handleSearchChange, handleDeleteSelected } =
    useCarnetDeSante();
  const [options, setOptions] = useState<Option[]>([]);

  // État pour contrôler l'entrée de recherche
  const [inputValue, setInputValue] = useState("");

  // Limiter le nombre d'options affichées pour améliorer les performances
  const MAX_OPTIONS = 100;

  useEffect(() => {
    setOptions(getOption(type));
  }, [type, getOption]);

  // Filtrer les options de manière optimisée avec useMemo
  const filteredOptions = useMemo(() => {
    if (!inputValue || inputValue.length < 2) {
      return options.slice(0, MAX_OPTIONS); // Limiter le nombre initial d'options
    }

    const search = inputValue.toLowerCase();

    if (type === TITRES_CARNET_DE_SANTE.medicaments) {
      return options
        .filter(
          (opt: Option) =>
            opt.nom.toLowerCase().includes(search) ||
            (opt.dosage && opt.dosage.toLowerCase().includes(search)) ||
            (opt.forme && opt.forme.toLowerCase().includes(search))
        )
        .slice(0, MAX_OPTIONS);
    }

    return options
      .filter((opt: Option) => opt.nom.toLowerCase().includes(search))
      .slice(0, MAX_OPTIONS);
  }, [options, inputValue, type]);

  // Gérer le changement de valeur de manière optimisée
  const handleChange = useCallback(
    (
      _: React.SyntheticEvent,
      newValue: string | Option | null,
      reason: AutocompleteChangeReason
    ) => {
      if (!newValue || reason === "clear") return;

      const nom = typeof newValue === "string" ? newValue : newValue.nom;
      if (nom && !selectedSearch.includes(nom)) {
        handleSearchChange(nom);
      }
    },
    [selectedSearch, handleSearchChange]
  );

  // Gérer le changement de l'entrée
  const handleInputChange = useCallback(
    (
      _: React.SyntheticEvent,
      newInputValue: string,
      reason: AutocompleteInputChangeReason
    ) => {
      setInputValue(newInputValue);
    },
    []
  );

  // Gérer la suppression d'un élément sélectionné
  const handleDelete = useCallback(
    (item: string) => {
      handleDeleteSelected(item);
    },
    [handleDeleteSelected]
  );

  return (
    <>
      {type !== TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage && (
        <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
          Si vous ne trouvez pas un élément sur la liste, tapez son nom et
          appuyez "Entrée" sur le clavier pour le rajouter
        </Typography>
      )}
      {type !== TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage &&
        type !== TITRES_CARNET_DE_SANTE.medicaments && (
          <Autocomplete
            freeSolo
            options={filteredOptions}
            getOptionLabel={(option) =>
              typeof option === "string" ? option : option.nom
            }
            inputValue={inputValue}
            onInputChange={handleInputChange}
            onChange={handleChange}
            renderInput={(params) => (
              <TextField
                {...params}
                placeholder="Recherche par nom"
                fullWidth
              />
            )}
            disableListWrap
            slotProps={{
              listbox: {
                style: { maxHeight: "200px" },
              },
            }}
          />
        )}
      {type === TITRES_CARNET_DE_SANTE.medicaments && (
        <Autocomplete
          options={filteredOptions}
          getOptionLabel={(option) =>
            typeof option === "string" ? option : option.nom
          }
          inputValue={inputValue}
          onInputChange={handleInputChange}
          onChange={handleChange}
          renderOption={(
            props,
            option: { id: number; nom: string; dosage: string; forme: string }
          ) => (
            <Box
              component="li"
              {...props}
              sx={{
                display: "relative",
              }}
            >
              <span>{typeof option === "string" ? option : option.nom}</span>
              <LightTooltip
                title={
                  <div>
                    <h1 className="text-sm">
                      <span className="font-semibold text-orange">Forme:</span>{" "}
                      <span className="text-black capitalize">
                        {option.forme}
                      </span>
                    </h1>
                    <h1 className="text-sm">
                      <span className="font-semibold text-orange">Dosage:</span>{" "}
                      <span className="text-black capitalize">
                        {option.dosage}
                      </span>
                    </h1>
                  </div>
                }
              >
                <Info size={20} className="text-gray-400 absolute right-1" />
              </LightTooltip>
            </Box>
          )}
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder="Recherche par nom, dosage ou forme"
              fullWidth
            />
          )}
          disableListWrap
          slotProps={{
            listbox: {
              style: { maxHeight: "200px" },
            },
          }}
        />
      )}
      {selectedSearch.length > 0 && (
        <Box sx={{ mt: 2, display: "flex", gap: 1, flexWrap: "wrap" }}>
          {selectedSearch.map((item) => (
            <Chip
              key={item}
              label={item}
              onDelete={() => handleDelete(item)}
              color="primary"
            />
          ))}
        </Box>
      )}

      {selectedSearch.map((item) => (
        <RenderFormFields key={item} item={item} type={type} />
      ))}
      {type === TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage && (
        <RenderFormFields type={type} />
      )}
    </>
  );
};
